{"name": "shiyin-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "translate:init": "node src/tools/translate-messages.mjs init", "translate:update": "node src/tools/translate-messages.mjs update", "translate:single": "node src/tools/translate-blogs.mjs single", "translate:batch": "node src/tools/translate-blogs.mjs batch"}, "dependencies": {"@azure/identity": "^4.0.0", "@azure/openai": "^2.0.0", "@ffmpeg/ffmpeg": "^0.12.15", "@ffmpeg/util": "^0.12.2", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.5", "@sentry/nextjs": "^8.35.0", "@supabase/supabase-js": "^2.45.4", "@uppy/audio": "^2.1.3", "@uppy/aws-s3": "^4.2.3", "@uppy/aws-s3-multipart": "^4.0.0", "@uppy/core": "^4.4.6", "@uppy/golden-retriever": "^4.1.1", "@uppy/informer": "^4.2.1", "@uppy/progress-bar": "^4.2.1", "@uppy/status-bar": "^4.1.3", "@uppy/url": "^4.2.4", "@uppy/xhr-upload": "^4.3.3", "axios": "^1.7.5", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.4", "date-fns": "^4.1.0", "dotenv": "^16.0.3", "flag-icons": "^7.3.2", "framer-motion": "^12.4.7", "gray-matter": "^4.0.3", "html-to-image": "^1.11.11", "lodash": "^4.17.21", "lucide-react": "^0.428.0", "markmap-lib": "^0.17.0", "markmap-view": "^0.17.0", "next": "^14.2.7", "next-intl": "^3.9.5", "openai": "^4.86.2", "posthog-js": "^1.266.0", "posthog-node": "^5.8.4", "react": "^18", "react-countdown": "^2.3.6", "react-dom": "^18", "react-markdown": "^9.0.1", "react-player": "^3.0.0-canary.0", "react-syntax-highlighter": "^15.6.1", "remark": "^15.0.1", "remark-gfm": "^4.0.1", "remark-html": "^16.0.1", "sonner": "^2.0.3", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "wasm-feature-detect": "^1.8.0", "zustand": "^5.0.1"}, "devDependencies": {"eslint": "^8", "eslint-config-next": "14.2.5", "postcss": "^8", "tailwindcss": "^3.4.1"}}