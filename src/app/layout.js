// This is the root layout that Next.js uses before any locale-specific layouts
import { Inter } from "next/font/google";
import "@/app/globals.css";
import { PostHogProvider } from "../components/PostHogProvider";

const inter = Inter({ subsets: ["latin"] });

export const viewport = {
  width: "device-width",
  initialScale: 1,
};

export default function RootLayout({ children }) {
  // 注意：实际的语言属性将由 [locale] 布局中的 NextIntlClientProvider 处理
  // 这里设置默认值，但会在客户端被覆盖
  return (
    // https://nextjs.org/docs/messages/react-hydration-error#solution-3-using-suppresshydrationwarning
    <html suppressHydrationWarning className={inter.className}>
      <body>
        <PostHogProvider>
          {children}
        </PostHogProvider>
      </body>
    </html>
  );
}