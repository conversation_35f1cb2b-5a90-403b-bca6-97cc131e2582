"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { AlertTriangle, Check, X, Loader2 } from "lucide-react";
import { useTranslations } from "next-intl";
import { subscriptionService } from "@/services/api/subscriptionService";
import { useUpgradeDialogStore } from "@/stores/useUpgradeDialogStore";
import { trackEvent } from "@/lib/analytics";
import { safeOpenUrl } from "@/lib/browserUtils";

const FreeUserUpgradeDialog = ({ isOpen, onOpenChange, limitType }) => {
  const t = useTranslations("dashboard.freeUserUpgradeDialog");
  const [isUpgrading, setIsUpgrading] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState("yearly"); // 默认选择年付
  const { openDialog } = useUpgradeDialogStore();

  const getDialogContent = () => {
    switch (limitType) {
      case "minutes":
        return {
          description: t("minutes.description"),
        };
      case "daily_count":
        return {
          description: t("daily_count.description"),
        };
      default:
        return {
          description: t("default.description"),
        };
    }
  };

  const content = getDialogContent();

  // 统一的关闭处理函数，包含事件追踪
  const handleClose = (closeMethod = "unknown") => {
    // 跟踪关闭事件
    trackEvent("free_user_upgrade_dialog_close", {
      limitType: limitType,
      source: "free_user_upgrade_dialog",
      closeMethod: closeMethod, // "close_button", "escape_key", "upgrade_success", "see_all_plans"
    });

    onOpenChange(false);
  };

  // 添加键盘事件处理
  useEffect(() => {
    const handleKeyDown = (event) => {
      if (event.key === "Escape" && isOpen) {
        handleClose("escape_key");
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleKeyDown);
      // 防止背景滚动
      document.body.style.overflow = "hidden";
    }

    return () => {
      document.removeEventListener("keydown", handleKeyDown);
      document.body.style.overflow = "unset";
    };
  }, [isOpen, onOpenChange]);

  const handleUpgrade = async () => {
    setIsUpgrading(true);
    try {
      // 根据选择的计划类型确定 plan ID
      const planId =
        selectedPlan === "yearly" ? "basic_yearly" : "basic_monthly";

      const response = await subscriptionService.createCheckoutSession(
        planId,
        null, // 不使用促销码
        "subscription",
        "free_user_upgrade_dialog"
      );

      const url = response.data.url;
      if (url) {
        safeOpenUrl(url);
        handleClose("upgrade_success");
      }
    } catch (error) {
      console.error("Failed to create checkout session:", error);

      // 跟踪升级失败事件
      trackEvent("free_user_upgrade_failed", {
        limitType: limitType,
        source: "free_user_upgrade_dialog",
        selectedPlan: selectedPlan,
        error: error.message || "Unknown error",
      });

      // 可以在这里添加用户友好的错误提示
      // 例如显示一个toast或者错误消息
    } finally {
      setIsUpgrading(false);
    }
  };

  const handleSeeAllPlans = () => {
    openDialog({
      source: "free_user_upgrade_dialog_see_all_plans",
      defaultPlanType: "yearly",
      title: "",
      description: "",
    });

    handleClose("see_all_plans");
  };

  // 移除点击背景关闭功能 - 用户需要明确选择操作

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl max-w-md w-full relative shadow-xl border border-gray-200">
        {/* Close Button */}
        <button
          onClick={() => handleClose("close_button")}
          className="absolute top-4 right-4 p-2 hover:bg-gray-100 rounded-full transition-colors"
        >
          <X className="w-5 h-5 text-gray-500" />
        </button>

        {/* Header Section - Warning area */}
        <div className="text-center pt-6 pb-4 px-6 bg-gradient-to-br from-slate-50 via-indigo-50/60 to-purple-50/40 rounded-t-2xl">
          <div className="w-12 h-12 bg-gradient-to-br from-indigo-100 to-purple-100 rounded-full flex items-center justify-center mx-auto mb-3 shadow-sm">
            <AlertTriangle className="w-6 h-6 text-indigo-500" />
          </div>
          <h2 className="text-xl text-gray-800 mb-1">{t("title")}</h2>
          <p className="text-sm text-slate-600">{content.description}</p>
        </div>

        {/* Plan Selection Section - Upgrade area */}
        <div className="px-6 py-6 bg-white rounded-b-2xl">
          {/* Features Card */}
          <div className="bg-gradient-to-br from-slate-50/80 to-indigo-50/60 rounded-xl p-4 mb-5 border border-slate-200/40">
            <div className="mb-3">
              <div className="flex items-center gap-2 mb-3">
                <div className="bg-gradient-to-r from-indigo-500 to-purple-500 text-white px-2 py-1 text-xs shadow-sm rounded-md flex items-center gap-1">
                  ⚡ {t("flashBadge")}
                </div>
                <span className="text-lg text-gray-800">{t("planTitle")}</span>
              </div>
            </div>

            {/* Features List */}
            <div className="space-y-2.5">
              <div className="flex items-center gap-3">
                <div className="w-4 h-4 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-full flex items-center justify-center shadow-sm">
                  <Check className="w-2.5 h-2.5 text-white" />
                </div>
                <span className="text-sm text-gray-600">
                  {t("features.minutes")}
                </span>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-4 h-4 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-full flex items-center justify-center shadow-sm">
                  <Check className="w-2.5 h-2.5 text-white" />
                </div>
                <span className="text-sm text-gray-600">
                  {t("features.noLimit")}
                </span>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-4 h-4 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-full flex items-center justify-center shadow-sm">
                  <Check className="w-2.5 h-2.5 text-white" />
                </div>
                <span className="text-sm text-gray-600">
                  {t("features.noRetention")}
                </span>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-4 h-4 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-full flex items-center justify-center shadow-sm">
                  <Check className="w-2.5 h-2.5 text-white" />
                </div>
                <span className="text-sm text-gray-600">
                  {t("features.aiFeatures")}
                </span>
              </div>
            </div>
          </div>

          {/* Plan Options */}
          <div className="bg-white rounded-xl border border-gray-200 mb-4">
            {/* Yearly Option */}
            <div
              className="flex items-center justify-between p-4 cursor-pointer"
              onClick={() => setSelectedPlan("yearly")}
            >
              <div className="flex items-start gap-3">
                <div
                  className={`w-4 h-4 border-2 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5 ${
                    selectedPlan === "yearly"
                      ? "border-indigo-500 bg-indigo-500"
                      : "border-gray-300"
                  }`}
                >
                  {selectedPlan === "yearly" && (
                    <div className="w-2 h-2 bg-white rounded-full"></div>
                  )}
                </div>
                <div className="flex flex-col leading-tight">
                  <div className="text-sm text-gray-800">
                    {t("pricing.yearly")}
                  </div>
                  <div className="text-sm text-gray-800">
                    {t("pricing.yearlyPrice").split("/")[0]}
                    <span className="text-gray-500">
                      /{t("pricing.yearlyPrice").split("/")[1] || "mo"}
                    </span>
                    <span className="text-xs text-gray-400 ml-1">
                      ({t("pricing.yearlyTotal")})
                    </span>
                  </div>
                </div>
              </div>
              <div className="bg-gradient-to-r from-emerald-500 to-green-500 text-white px-3 py-1 text-xs shadow-sm rounded-full">
                {t("pricing.save40")}
              </div>
            </div>

            {/* Divider */}
            <div className="border-t border-gray-200"></div>

            {/* Monthly Option */}
            <div
              className="flex items-center p-4 cursor-pointer"
              onClick={() => setSelectedPlan("monthly")}
            >
              <div className="flex items-start gap-3">
                <div
                  className={`w-4 h-4 border-2 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5 ${
                    selectedPlan === "monthly"
                      ? "border-indigo-500 bg-indigo-500"
                      : "border-gray-300"
                  }`}
                >
                  {selectedPlan === "monthly" && (
                    <div className="w-2 h-2 bg-white rounded-full"></div>
                  )}
                </div>
                <div className="flex flex-col leading-tight">
                  <div className="text-sm text-gray-800">
                    {t("pricing.monthly")}
                  </div>
                  <div className="text-sm text-gray-800">
                    {t("pricing.monthlyPrice").split("/")[0]}
                    <span className="text-gray-500">
                      /{t("pricing.monthlyPrice").split("/")[1] || "mo"}
                    </span>
                    <span className="text-xs text-gray-400 ml-1">
                      ({t("pricing.monthlyTotal")})
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Upgrade Button */}
          <Button
            className="w-full bg-gradient-to-r from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700 text-white py-3 rounded-lg shadow-sm disabled:opacity-50 disabled:cursor-not-allowed"
            onClick={handleUpgrade}
            disabled={isUpgrading}
          >
            {isUpgrading ? (
              <div className="flex items-center justify-center gap-2">
                <Loader2 className="w-4 h-4 animate-spin" />
                {t("upgrading")}
              </div>
            ) : (
              <>🚀 {t("upgradeButton")}</>
            )}
          </Button>

          {/* See All Plans Button */}
          <div className="text-center mt-3">
            <button
              className="text-sm text-gray-500 hover:text-gray-700 underline"
              onClick={handleSeeAllPlans}
            >
              {t("seeAllPlans")}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FreeUserUpgradeDialog;
