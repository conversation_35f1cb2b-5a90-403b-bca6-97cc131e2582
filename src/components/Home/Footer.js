"use client";

import { Link } from "@/components/Common/Link";
import { Twitter, Mail } from "lucide-react";
import Logo from "@/components/Logo";
import { useTranslations } from "next-intl";
import Mentions from "@/components/Home/Mentions";

// Free Tools data
const defaultFreeTools = [
  {
    href: "/tools/video-to-audio-extractor",
    label: "toolsList.videoToAudio",
  },
  {
    href: "/tools/wav-to-mp3-converter",
    label: "toolsList.wavToMp3",
  },
];

// Partners data
const defaultPartners = [];

// Free Tools Component
const FreeToolsSection = ({ tools }) => {
  const t = useTranslations("footer");

  return (
    <div>
      <p className="text-sm font-semibold text-gray-400 tracking-wider uppercase">
        {t("sections.tools")}
      </p>
      <ul className="mt-4 space-y-3">
        {tools.map((tool) => (
          <li key={tool.href}>
            <Link
              href={tool.href}
              className="text-sm text-gray-500 hover:text-gray-900"
            >
              {t(tool.label)}
            </Link>
          </li>
        ))}
      </ul>
    </div>
  );
};

/**
 * Partners section component
 * @param {Object} props
 * @param {Array<{ href: string, name: string }>} partners - Array of partner links
 */
const PartnersSection = ({ partners }) => {
  const t = useTranslations("footer");

  return (
    <div>
      <p className="text-sm font-semibold text-gray-400 tracking-wider uppercase">
        {t("sections.partners")}
      </p>
      <ul className="mt-4 space-y-3">
        {partners.map((partner) => (
          <li key={partner.href}>
            <Link
              href={partner.href}
              className="text-sm text-gray-500 hover:text-gray-900"
              target="_blank"
              rel="nofollow noopener"
            >
              {partner.name}
            </Link>
          </li>
        ))}
      </ul>
    </div>
  );
};

/**
 * @typedef {Object} FreeToolItem
 * @property {string} href - The URL path for the tool
 * @property {string} name - The display name of the tool
 */

/**
 * @typedef {Object} FooterProps
 * @property {Array<{ href: string, name: string }>} [freeTools] - Array of free tools
 * @property {boolean} [showPartners=true] - Whether to show the partners section
 */

/**
 * Footer component for the application
 * @example
 * // Default footer with partners (only shown on homepage)
 * <Footer />
 *
 * // Custom free tools
 * <Footer freeTools={customTools} />
 *
 * @param {FooterProps} props
 */
const Footer = ({ freeTools = defaultFreeTools }) => {
  const t = useTranslations("footer");
  // 暂时不展示 partner
  const showPartners = false;

  const companyLinks = [
    {
      label: "links.terms",
      href: "/terms-of-service",
    },
    {
      label: "links.privacy",
      href: "/privacy-policy",
    },
    {
      label: "links.security",
      href: "/security",
    },
    {
      label: "links.support",
      href: "mailto:<EMAIL>",
      title: t("links.emailTitle"),
    },
    {
      label: "links.discord",
      href: "https://discord.gg/RJTaS28UWU",
      title: t("links.discordTitle"),
    },
  ];

  return (
    <footer className="bg-gradient-to-b from-custom-bg-50 via-white to-custom-bg-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        <div className="flex flex-col md:flex-row justify-between items-start mb-8 my-10">
          <div className="flex flex-col space-y-8 mb-8 md:mb-0 md:w-1/3">
            <div>
              <Logo />
              <div className="flex mt-4 space-x-6">
                <Link
                  href="https://x.com/UniscribeHQ"
                  className="text-gray-400 hover:text-gray-500"
                >
                  <span className="sr-only">Twitter</span>
                  <Twitter className="h-6 w-6" aria-hidden="true" />
                </Link>
                <Link
                  href="mailto:<EMAIL>"
                  className="text-gray-400 hover:text-gray-500"
                >
                  <span className="sr-only">Email</span>
                  <Mail className="h-6 w-6" />
                </Link>
              </div>
            </div>
          </div>

          <div className="flex flex-col justify-between w-full md:w-2/3">
            <div className="grid grid-cols-2 gap-x-8 gap-y-12 sm:grid-cols-3 md:gap-x-12 md:gap-y-0">
              <FreeToolsSection tools={freeTools} />
              {showPartners && <PartnersSection partners={defaultPartners} />}
              <div>
                <p className="text-sm font-semibold text-gray-400 tracking-wider uppercase">
                  {t("sections.company")}
                </p>
                <ul className="mt-4 space-y-3">
                  {companyLinks.map((item) => (
                    <li key={item.href}>
                      <Link
                        href={item.href}
                        className="text-sm text-gray-500 hover:text-gray-900"
                        title={item.title}
                        {...(item.href.startsWith("http") ||
                        item.href.startsWith("mailto:")
                          ? { target: "_blank", rel: "noopener noreferrer" }
                          : {})}
                      >
                        {t(item.label)}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-8 border-t border-gray-200 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <p className="text-base text-gray-400">
              © {new Date().getFullYear()} VanCode LLC. All rights reserved.
            </p>
            <Mentions />
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
