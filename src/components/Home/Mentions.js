"use client";

import { Link } from "@/components/Common/Link";
import { useEffect, useState } from "react";

const mentions = [
  {
    title: "Product Hunt",
    url: "https://www.producthunt.com/products/uniscribe?utm_source=badge-featured#uniscribe",
    logo: "/badges/producthunt.svg",
    alt: "UniScribe - Featured on Product Hunt",
  },
  {
    title: "Theresanaiforthat",
    url: "https://theresanaiforthat.com/ai/uniscribe/",
  },
  {
    title: "TopAITools",
    url: "https://topai.tools/t/uniscribe-co",
  },
];

const Mentions = () => {
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % mentions.length);
    }, 3000);

    return () => clearInterval(timer);
  }, []);

  return (
    <div className="flex items-center gap-4">
      <p className="text-sm font-semibold text-gray-400 tracking-wider uppercase hidden md:block">
        Find us on
      </p>
      <div className="sr-only">
        {mentions.map((mention) => (
          <a key={mention.title} href={mention.url}>
            {mention.title}
          </a>
        ))}
      </div>
      <div className="overflow-hidden h-8 w-40 relative" aria-hidden="true">
        {mentions.map((mention, index) => (
          <Link
            key={mention.title}
            href={mention.url}
            target="_blank"
            rel="noopener"
            className={`absolute transition-all duration-500 transform ${
              index === currentIndex
                ? "translate-y-0 opacity-100"
                : "translate-y-full opacity-0"
            }`}
          >
            {mention.logo ? (
              <img
                src={mention.logo}
                alt={mention.alt}
                className="h-6 w-auto"
              />
            ) : (
              <span className="text-sm font-medium text-gray-600">
                {mention.title}
              </span>
            )}
          </Link>
        ))}
      </div>
    </div>
  );
};

export default Mentions;
