import { <PERSON> } from "lucide-react";
import { Truculenta } from "next/font/google";

export const FEATURES = {
  CHRISTMAS_BANNER: {
    enabled: false,
    startDate: "2024-12-19T00:00:00Z",
    endDate: "2024-12-31T23:59:59Z",
    promoCode: "XMAS40",
    discount: "40% OFF",
  },
  MAINTENANCE_NOTICE: {
    enabled: false,
    message:
      "System maintenance in progress. Some features may be temporarily unavailable.",
    startTime: "2025-07-15 22:00",
    endTime: "2025-07-16 02:00",
    details: [
      "Transcription service will be temporarily unavailable",
      "Completed transcriptions will still be accessible",
      "Account management and payment functions are not affected",
    ],
  },
  INCIDENT_NOTICE: {
    enabled: false,
    severity: "resolved", // "critical", "major", "minor", "resolved"
    title: "Authentication Service Disruption",
    message:
      "Supabase (our authentication provider) experienced a service disruption. Services are now restored and being monitored.",
    incidentTime: "2025-06-12 18:30",
    resolvedTime: "2025-06-12 21:17",
    statusUrl: "https://status.supabase.com/",
    updates: [
      {
        time: "2025-06-12 21:17",
        status: "Monitoring",
        message:
          "Services have been restored and error rates have returned to baseline. We continue to monitor for any further issues.",
      },
      {
        time: "2025-06-12 20:36",
        status: "Update",
        message:
          "Services are recovering quickly. We are seeing improvement in error rates.",
      },
      {
        time: "2025-06-12 18:31",
        status: "Investigating",
        message:
          "We are seeing widespread reports of connectivity issues. Investigation in progress.",
      },
    ],
    affectedServices: [
      "User authentication and login",
      "Account registration",
      "Password reset functionality",
    ],
    unaffectedServices: [
      "Transcription processing",
      "File uploads and downloads",
      "Existing user sessions",
    ],
  },
  PROMO_CARD: {
    enabled: true,
    discountText: "40% OFF",
    discountPercentage: 40,
    plan: "basic_yearly",
    validFor: "basic_plan",
    duration: "one_year_duration",
    promoCode: "",
  },
  PRICE_INCREASE: {
    enabled: true,
    tiers: ["lite"],
    startDate: "2025-04-23T00:00:00Z",
    endDate: "2025-04-30T00:00:00Z",
  },
  YOUTUBE_UPGRADE: {
    enabled: process.env.NEXT_PUBLIC_YOUTUBE_UPGRADE_ENABLED === "true",
  },
};
